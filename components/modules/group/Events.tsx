import {
  View,
  Text,
  ScrollView,
  Image,
  TouchableOpacity,
  Pressable,
  StyleSheet,
  RefreshControl,
} from "react-native";
import { useLiveClasses } from "@/lib/api/queries";
import { useState, useCallback } from "react";
import type { LiveClass } from "@/lib/api/types";
import { router } from "expo-router";

interface Props {
  moduleId: number;
  groupId: string;
  cohortId: string;
}

export const Events = ({ moduleId, groupId, cohortId }: Props) => {
  const [activeTab, setActiveTab] = useState<"upcoming" | "past">("upcoming");
  const [refreshing, setRefreshing] = useState(false);

  const { data: upcomingLiveClasses, refetch: refetchUpcoming } =
    useLiveClasses(groupId, cohortId, moduleId, true);
  const { data: pastLiveClasses, refetch: refetchPast } = useLiveClasses(
    groupId,
    cohortId,
    moduleId,
    false
  );

  const onRefresh = useCallback(async () => {
    console.log("Events: Pull to refresh started, active tab:", activeTab);
    setRefreshing(true);
    try {
      console.log(
        "Events: Calling refetch for both upcoming and past events..."
      );
      await Promise.all([refetchUpcoming(), refetchPast()]);
      console.log("Events: Refetch completed successfully");
    } catch (error) {
      console.error("Events: Refetch failed:", error);
    } finally {
      setRefreshing(false);
      console.log("Events: Pull to refresh finished");
    }
  }, [refetchUpcoming, refetchPast, activeTab]);

  const handleEventPress = (event: LiveClass) => {
    router.push(
      `/groups/${groupId}/cohorts/${cohortId}/modules/${moduleId}/live-classes/${event.id}`
    );
  };

  const getTimeUntilStart = (startTime: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor(
      (startTime.getTime() - now.getTime()) / (1000 * 60)
    );
    if (diffInMinutes <= 60 && diffInMinutes > 0) {
      return `Starting in ${diffInMinutes} mins`;
    }
    return null;
  };

  const renderEventCard = (event: LiveClass, isUpcoming: boolean) => {
    const startTime = new Date(event.startAt);
    const endTime = new Date(event.endsAt);
    const timeString = `${startTime.toLocaleString("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    })} - ${endTime.toLocaleString("en-US", {
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    })}`;

    const startingSoon = isUpcoming ? getTimeUntilStart(startTime) : null;
    const dateString = startTime.toLocaleString("en-US", {
      month: "short",
      day: "numeric",
    });

    return (
      <Pressable
        key={event.id}
        onPress={() => handleEventPress(event)}
        style={styles.eventCard}
      >
        <View style={styles.eventHeader}>
          <Text style={styles.timeText}>{timeString}</Text>
          <View style={styles.dateContainer}>
            {event.isRegistered && (
              <View style={styles.goingTag}>
                <Text style={styles.goingTagText}>Going</Text>
              </View>
            )}
            <Text
              style={[
                styles.dateText,
                startingSoon ? styles.startingSoonText : styles.regularDateText,
              ]}
            >
              {startingSoon || dateString}
            </Text>
          </View>
        </View>

        <View style={styles.eventContent}>
          <Text style={styles.eventTitle}>{event.topic}</Text>
          <Text style={styles.eventSubtitle}>Weekly Training</Text>
        </View>

        <View style={styles.participantsContainer}>
          <View style={styles.participantsWrapper}>
            <View style={styles.avatarsContainer}>
              {[1, 2, 3].map((_, index) => (
                <View
                  key={index}
                  style={[styles.avatar, index > 0 && styles.avatarOverlap]}
                />
              ))}
            </View>
            <Text style={styles.participantsText}>
              {event.numOfParticipants} Participants
            </Text>
          </View>
        </View>
      </Pressable>
    );
  };

  const groupEventsByMonth = (events: LiveClass[]) => {
    const grouped: { [key: string]: { [key: string]: LiveClass[] } } = {};
    const today = new Date();
    const todayStr = today.toDateString();

    events?.forEach((event) => {
      const eventDate = new Date(event.startAt);
      const monthKey = eventDate.toLocaleString("en-US", { month: "long" });
      const dateKey =
        eventDate.toDateString() === todayStr
          ? "Today"
          : eventDate.toLocaleString("en-US", {
              month: "short",
              day: "numeric",
            });

      if (!grouped[monthKey]) {
        grouped[monthKey] = {};
      }
      if (!grouped[monthKey][dateKey]) {
        grouped[monthKey][dateKey] = [];
      }
      grouped[monthKey][dateKey].push(event);
    });

    return grouped;
  };

  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        {["Upcoming", "Past"].map((tab) => (
          <Pressable
            key={tab}
            style={[
              styles.tab,
              activeTab.toLowerCase() === tab.toLowerCase() && styles.activeTab,
            ]}
            onPress={() =>
              setActiveTab(tab.toLowerCase() as "upcoming" | "past")
            }
          >
            <Text
              style={[
                styles.tabText,
                activeTab.toLowerCase() === tab.toLowerCase() &&
                  styles.activeTabText,
              ]}
            >
              {tab}
            </Text>
          </Pressable>
        ))}
      </View>

      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#fff"
            colors={["#fff"]}
          />
        }
      >
        {activeTab === "upcoming" && (
          <>
            {upcomingLiveClasses?.data?.liveClasses?.length === 0 ? (
              <View style={styles.emptyStateContainer}>
                <Text style={styles.emptyStateTitle}>No Upcoming Events</Text>
                <Text style={styles.emptyStateSubtitle}>
                  Stay tuned! New events will be posted here.
                </Text>
              </View>
            ) : (
              upcomingLiveClasses?.data?.liveClasses &&
              Object.entries(
                groupEventsByMonth(upcomingLiveClasses.data.liveClasses)
              ).map(([month, dates]) => (
                <View key={month}>
                  <Text style={styles.monthHeader}>{month}</Text>
                  {Object.entries(dates).map(([date, events]) => (
                    <View key={date}>
                      {events.map((event) => renderEventCard(event, true))}
                    </View>
                  ))}
                </View>
              ))
            )}
          </>
        )}
        {activeTab === "past" && (
          <>
            {pastLiveClasses?.data?.liveClasses?.length === 0 ? (
              <View style={styles.emptyStateContainer}>
                <Text style={styles.emptyStateTitle}>No Past Events</Text>
                <Text style={styles.emptyStateSubtitle}>
                  Past events will appear here once they're completed.
                </Text>
              </View>
            ) : (
              pastLiveClasses?.data?.liveClasses &&
              Object.entries(
                groupEventsByMonth(pastLiveClasses.data.liveClasses)
              ).map(([month, dates]) => (
                <View key={month}>
                  <Text style={styles.monthHeader}>{month}</Text>
                  {Object.entries(dates).map(([date, events]) => (
                    <View key={date}>
                      {events.map((event) => renderEventCard(event, false))}
                    </View>
                  ))}
                </View>
              ))
            )}
          </>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  // container: {
  //   flex: 1,
  // },
  container: {
    // flex: 1,
    height: "100%",
    paddingVertical: 16,
    paddingTop: 10,
  },
  tabContainer: {
    flexDirection: "row",
    paddingTop: 12,
    paddingBottom: 24,
    gap: 8,
  },
  tab: {
    paddingVertical: 6,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: "#2C2E33",
  },
  activeTab: {
    backgroundColor: "#FFFFFF",
  },
  tabText: {
    color: "#6B7280",
    fontSize: 14,
    fontWeight: "500",
  },
  activeTabText: {
    color: "#000000",
  },
  monthHeader: {
    color: "#FFFFFF",
    fontSize: 28,
    fontWeight: "600",
    marginBottom: 24,
  },
  eventCard: {
    backgroundColor: "#1E1E1E",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  eventHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  timeText: {
    color: "#9B9B9B",
    fontSize: 14,
  },
  dateText: {
    fontSize: 16,
    fontWeight: "600",
  },
  startingSoonText: {
    color: "#4CAF50",
  },
  regularDateText: {
    color: "#FFFFFF",
  },
  eventContent: {
    marginTop: 8,
  },
  eventTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
  },
  eventSubtitle: {
    color: "#9B9B9B",
    fontSize: 16,
    marginTop: 4,
  },
  participantsContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
  },
  participantsWrapper: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatarsContainer: {
    flexDirection: "row",
    marginRight: 8,
  },
  avatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#666",
    borderWidth: 2,
    borderColor: "#1E1E1E",
  },
  avatarOverlap: {
    marginLeft: -12,
  },
  participantsText: {
    color: "#9B9B9B",
    marginLeft: 4,
  },
  goingTag: {
    backgroundColor: "rgba(76, 175, 80, 0.1)",
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "rgba(76, 175, 80, 0.3)",
  },
  goingTagText: {
    color: "#4CAF50",
    fontSize: 10,
    fontWeight: "500",
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
    paddingHorizontal: 24,
  },
  emptyStateTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 8,
  },
  emptyStateSubtitle: {
    color: "#9B9B9B",
    fontSize: 16,
    textAlign: "center",
    lineHeight: 22,
  },
});
