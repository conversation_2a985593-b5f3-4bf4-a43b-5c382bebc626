import { useEffect, useState, useCallback, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
} from "react-native";
import { type EnrichedActivity } from "getstream";
import { Heart, MessageCircle, Pin } from "lucide-react-native";
import { Image } from "expo-image";
import { router, useFocusEffect } from "expo-router";

import { useAppContext } from "@/context/app";
import type { FeedModule } from "@/lib/api/types";

interface Props {
  module: FeedModule;
}

interface EnrichedActivityWithText extends EnrichedActivity {
  text?: string;
  message?: string;
  image?: string;
  isPinned?: boolean;
  own_reactions?: {
    like?: any[];
  };
  reaction_counts?: {
    like?: number;
    comment?: number;
  };
}

export const Feed = ({ module }: Props) => {
  const { streamClient } = useAppContext();
  const [activities, setActivities] = useState<EnrichedActivityWithText[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const hasInitialLoad = useRef(false);

  console.log("Feed module config:", module.config);

  const fetchActivities = useCallback(
    async (isRefreshing = false) => {
      if (!streamClient || !module) return;

      try {
        if (!isRefreshing) setLoading(true);
        setError(null);

        // Get the feed using the config from the module
        const feed = streamClient.feed(
          module.config.feedGroup,
          module.config.feedId
        );

        // Fetch activities with enrichment
        const response = await feed.get({
          limit: 30,
          withReactionCounts: true,
          withOwnReactions: true,
        });

        console.log("Feed response:", response);
        setActivities(response.results as EnrichedActivityWithText[]);
        hasInitialLoad.current = true;
      } catch (err) {
        console.error("Error fetching feed:", err);
        setError("Failed to load feed. Please try again later.");
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    },
    [streamClient, module]
  );

  useEffect(() => {
    fetchActivities();
  }, [fetchActivities]);

  // Refresh feed when screen comes into focus (e.g., when navigating back from PostDetail)
  useFocusEffect(
    useCallback(() => {
      // Only fetch if we've done the initial load and we're not currently loading
      if (hasInitialLoad.current && !loading) {
        console.log("Refreshing feed on focus");
        fetchActivities(true);
      }
    }, [fetchActivities, loading])
  );

  const onRefresh = () => {
    setRefreshing(true);
    fetchActivities(true);
  };

  const handleLike = async (activityId: string) => {
    if (!streamClient) return;

    try {
      const activity = activities.find((a) => a.id === activityId);
      const isLiked =
        activity?.own_reactions?.like && activity.own_reactions.like.length > 0;

      if (isLiked) {
        // Unlike
        await streamClient.reactions.delete(
          activity.own_reactions!.like![0].id
        );

        setActivities((prevActivities) =>
          prevActivities.map((a) => {
            if (a.id === activityId) {
              return {
                ...a,
                own_reactions: { ...a.own_reactions, like: [] },
                reaction_counts: {
                  ...a.reaction_counts,
                  like: Math.max((a.reaction_counts?.like || 0) - 1, 0),
                },
              };
            }
            return a;
          })
        );
      } else {
        // Like
        const reaction = await streamClient.reactions.add("like", activityId);

        setActivities((prevActivities) =>
          prevActivities.map((a) => {
            if (a.id === activityId) {
              return {
                ...a,
                own_reactions: { ...a.own_reactions, like: [reaction] },
                reaction_counts: {
                  ...a.reaction_counts,
                  like: (a.reaction_counts?.like || 0) + 1,
                },
              };
            }
            return a;
          })
        );
      }
    } catch (error) {
      console.error("Error toggling like:", error);
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) return "just now";
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };

  const getActorName = (actor: any): string => {
    if (typeof actor === "string") {
      // Extract a readable name from strings like "SU:something:else"
      const parts = actor.split(":");
      return parts[parts.length - 1] || actor;
    }
    if (actor?.data?.name) return actor.data.name;
    if (actor?.data?.firstName && actor?.data?.lastName) {
      return `${actor.data.firstName} ${actor.data.lastName}`;
    }
    if (actor?.id) return `User ${actor.id.substring(0, 8)}`;
    return "Unknown User";
  };

  const getActorImage = (actor: any): string | null => {
    if (actor?.data?.image) return actor.data.image;
    if (actor?.data?.avatarUrl) return actor.data.avatarUrl;
    return null;
  };

  const getActivityContent = (activity: EnrichedActivityWithText): string => {
    if (activity.message) return activity.message;
    if (activity.text) return activity.text;
    if (typeof activity.object === "string") return activity.object;
    const obj = activity.object as any;
    if (obj?.text) return obj.text;
    if (obj?.content) return obj.content;
    return "";
  };

  const isCreator = (actor: any): boolean => {
    if (actor?.data?.role) {
      return actor.data.role === "creator" || actor.data.role === "moderator";
    }
    return false;
  };

  const renderEventCard = (activity: EnrichedActivityWithText) => {
    const eventData = (activity as any).eventData;
    if (!eventData) return null;

    return (
      <View style={styles.eventCard}>
        <View style={styles.eventTimeContainer}>
          <Text style={styles.eventTime}>{eventData.time}</Text>
          <Text style={styles.eventDate}>{eventData.date}</Text>
        </View>
        <View style={styles.eventDetails}>
          <Text style={styles.eventTitle}>{eventData.title}</Text>
          <Text style={styles.eventHost}>By {eventData.host}</Text>
          <View style={styles.eventParticipants}>
            {/* Placeholder for participant avatars */}
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" color="#EF5252" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity
          style={styles.retryButton}
          onPress={() => fetchActivities()}
        >
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const handlePostPress = (activity: EnrichedActivityWithText) => {
    router.push({
      pathname: "/post/[id]",
      params: {
        id: activity.id,
        moduleConfig: JSON.stringify(module.config),
      },
    });
  };

  console.log("Rendering activities:", activities.length);

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={onRefresh}
          tintColor="#EF5252"
        />
      }
    >
      {activities.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyText}>No posts yet. Check back later!</Text>
        </View>
      ) : (
        activities.map((activity) => (
          <TouchableOpacity
            key={activity.id}
            style={styles.postCard}
            onPress={() => handlePostPress(activity)}
            activeOpacity={0.8}
          >
            <View style={styles.postHeader}>
              <View style={styles.userInfo}>
                {getActorImage(activity.actor) ? (
                  <Image
                    source={{ uri: getActorImage(activity.actor)! }}
                    style={styles.avatar}
                  />
                ) : (
                  <View style={styles.avatarPlaceholder}>
                    <Text style={styles.avatarText}>
                      {getActorName(activity.actor)[0]?.toUpperCase() || "U"}
                    </Text>
                  </View>
                )}
                <View style={styles.userMeta}>
                  <View style={styles.nameContainer}>
                    <Text style={styles.userName}>
                      {getActorName(activity.actor)}
                    </Text>
                    {isCreator(activity.actor) && (
                      <View style={styles.creatorBadge}>
                        <Text style={styles.creatorText}>Creator</Text>
                      </View>
                    )}
                  </View>
                  <Text style={styles.timestamp}>
                    {formatTime(activity.time)}
                  </Text>
                </View>
              </View>
              {activity.isPinned && (
                <Pin size={20} color="#FACC15" fill="#FACC15" />
              )}
            </View>

            <View style={styles.postContent}>
              <Text style={styles.postText}>
                {getActivityContent(activity)}
              </Text>
              {activity.image && (
                <Image
                  source={{ uri: activity.image }}
                  style={styles.postImage}
                />
              )}
              {(activity as any).eventData && renderEventCard(activity)}
            </View>

            <View style={styles.postActions}>
              <TouchableOpacity
                style={styles.actionButton}
                onPress={(e) => {
                  e.stopPropagation();
                  handleLike(activity.id);
                }}
              >
                <Heart
                  size={20}
                  color={
                    activity.own_reactions?.like?.length ? "#EF5252" : "#fff"
                  }
                  fill={
                    activity.own_reactions?.like?.length
                      ? "#EF5252"
                      : "transparent"
                  }
                />
                <Text
                  style={[
                    styles.actionText,
                    activity.own_reactions?.like?.length
                      ? styles.likedText
                      : undefined,
                  ]}
                >
                  {activity.reaction_counts?.like || 0}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.actionButton}>
                <MessageCircle size={20} color="#fff" />
                <Text style={styles.actionText}>
                  {activity.reaction_counts?.comment || 0}
                </Text>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        ))
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    backgroundColor: "#000",
    paddingTop: 16,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
  },
  emptyContainer: {
    paddingTop: 100,
    alignItems: "center",
  },
  emptyText: {
    color: "#9A9A9A",
    fontSize: 16,
  },
  errorText: {
    color: "#EF5252",
    fontSize: 16,
    marginBottom: 16,
    textAlign: "center",
  },
  retryButton: {
    backgroundColor: "#EF5252",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  retryButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  postCard: {
    backgroundColor: "#171D23",
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  postHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 10,
  },
  avatar: {
    width: 38,
    height: 38,
    borderRadius: 19,
  },
  avatarPlaceholder: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: "#EF5252",
    justifyContent: "center",
    alignItems: "center",
  },
  avatarText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  userMeta: {
    gap: 2,
  },
  nameContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  userName: {
    fontSize: 14,
    fontWeight: "700",
    color: "#fff",
  },
  creatorBadge: {
    backgroundColor: "#EF5252",
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  creatorText: {
    fontSize: 10,
    fontWeight: "500",
    color: "#fff",
  },
  timestamp: {
    fontSize: 12,
    color: "#9A9A9A",
  },
  postContent: {
    marginBottom: 16,
  },
  postText: {
    fontSize: 14,
    color: "#D9D9D9",
    lineHeight: 20,
  },
  postImage: {
    width: "100%",
    height: 200,
    borderRadius: 8,
    marginTop: 12,
  },
  eventCard: {
    backgroundColor: "#5B6BDB",
    borderRadius: 8,
    padding: 16,
    marginTop: 12,
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  eventTimeContainer: {
    alignItems: "flex-end",
  },
  eventTime: {
    color: "#fff",
    fontSize: 14,
    opacity: 0.8,
  },
  eventDate: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "600",
  },
  eventDetails: {
    flex: 1,
  },
  eventTitle: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
    marginBottom: 4,
  },
  eventHost: {
    color: "#fff",
    fontSize: 12,
    opacity: 0.8,
    marginBottom: 8,
  },
  eventParticipants: {
    flexDirection: "row",
  },
  postActions: {
    flexDirection: "row",
    gap: 20,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
  },
  actionText: {
    fontSize: 14,
    fontWeight: "700",
    color: "rgba(255, 255, 255, 0.8)",
  },
  likedText: {
    color: "#EF5252",
  },
});
