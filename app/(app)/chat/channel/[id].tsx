import { useEffect, useState, useMemo } from "react";
import { useLocalSearchParams, router } from "expo-router";
import {
  SafeAreaView,
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
} from "react-native";
import { Channel, MessageList, MessageInput, Avatar } from "stream-chat-expo";
import { Channel as ChannelType } from "stream-chat";
import { Ionicons } from "@expo/vector-icons";
import { useAppContext } from "@/context/app";
import { CustomTypingIndicator } from "@/components/ui/TypingIndicator";

interface ChannelData {
  name?: string;
  member_count?: number;
  image?: string;
}

function EmptyState() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.emptyStateContainer}>
        <Ionicons name="chatbubble-outline" size={48} color="#4a4a4a" />
        <Text style={styles.emptyStateTitle}>No messages yet</Text>
        <Text style={styles.emptyStateText}>
          Start the conversation by sending a message
        </Text>
      </View>
    </SafeAreaView>
  );
}

function ChatChannel() {
  const { id } = useLocalSearchParams();
  const { chatClient, setThread, setChannel } = useAppContext();

  const channel = chatClient?.getChannelById("messaging", id as string, {}) as
    | (ChannelType & { data?: ChannelData })
    | undefined;

  const [showMembers, setShowMembers] = useState(false);
  const [channelName, setChannelName] = useState("Unnamed Channel");
  const [memberCount, setMemberCount] = useState(0);

  useEffect(() => {
    if (channel) {
      channel.watch().then(() => {
        setChannel(channel);
        setChannelName(channel.data?.name || "Unnamed Channel");
        setMemberCount(channel.data?.member_count || 0);
      });
    }
  }, [channel]);

  if (!channel) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Channel not found</Text>
      </SafeAreaView>
    );
  }

  const isPrivateChat = useMemo(
    () => !channel.data?.name && memberCount === 2,
    [channel.data?.name, memberCount],
  );

  // use memberCount to check if channel is ready (might have a better way)
  const isChannelReady = useMemo(() => memberCount > 0, [memberCount]);

  useEffect(() => {
    if (isPrivateChat) {
      const currentUserId = chatClient?.user?.id;
      const otherMember = Object.values(channel.state.members).find(
        (member) => member.user?.id !== currentUserId,
      );
      setChannelName(
        otherMember?.user?.name || otherMember?.user?.id || "Private Message",
      );
    }
  }, [isPrivateChat]);

  const channelImage =
    channel.data?.image || "https://getstream.io/random_png/?id=default";

  if (!isChannelReady) {
    return (
      <SafeAreaView style={styles.container}>
        <Text>Loading...</Text>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color="#fff" />
          </TouchableOpacity>
          {!isPrivateChat && (
            <TouchableOpacity
              style={styles.headerContentRight}
              onPress={() => router.push(`/chat/channel/${id}/members`)}
            >
              <View style={styles.avatarContainer}>
                <Avatar size={48} name={channelName} image={channelImage} />
              </View>
              <View style={styles.headerText}>
                <Text style={styles.channelName}>{channelName}</Text>
                <Text style={styles.memberCount}>{memberCount} members</Text>
              </View>
            </TouchableOpacity>
          )}
          {isPrivateChat && (
            <View style={styles.headerContentRight}>
              <View style={styles.avatarContainer}>
                <Avatar size={48} name={channelName} image={channelImage} />
              </View>
              <View style={styles.headerText}>
                <Text style={styles.channelName}>{channelName}</Text>
              </View>
            </View>
          )}
        </View>
      </View>
      <View style={styles.inner}>
        <View style={styles.channelWrapper}>
          <Channel channel={channel} TypingIndicator={() => null}>
            <MessageList
              EmptyStateIndicator={EmptyState}
              onThreadSelect={(thread) => {
                setThread(thread);
                router.push(
                  `/chat/channel/${channel.cid}/thread/${thread?.cid}`,
                );
              }}
            />
            <CustomTypingIndicator />
            <MessageInput />
          </Channel>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  header: {
    borderBottomWidth: 1,
    borderBottomColor: "#333",
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    paddingBottom: 16,
  },
  headerContentRight: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
  },
  inner: {
    flex: 1,
    padding: 16,
  },
  avatarContainer: {
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  channelName: {
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
    marginBottom: 4,
  },
  memberCount: {
    fontSize: 14,
    color: "#888",
  },
  subtitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  channelWrapper: {
    flex: 1,
    backgroundColor: "#1a1a1a",
    borderRadius: 12,
    overflow: "hidden",
    borderWidth: 0,
  },
  backButton: {
    marginRight: 8,
    padding: 4,
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 20,
    backgroundColor: "#000",
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
    marginTop: 16,
    marginBottom: 8,
  },
  emptyStateText: {
    fontSize: 14,
    color: "#888",
    textAlign: "center",
  },
});

export default ChatChannel;
