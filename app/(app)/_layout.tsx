import { useEffect, useState, useRef } from "react";
import { Stack, router } from "expo-router";
import SuperTokens from "supertokens-react-native";
import { Chat } from "stream-chat-expo";
import * as Device from "expo-device";
import * as Notifications from "expo-notifications";
import Constants from "expo-constants";
import { Platform } from "react-native";
import { getApp } from "@react-native-firebase/app";
import messaging from "@react-native-firebase/messaging";

import {
  useMyGroups,
  useProfile,
  useRegisterPushNotificationToken,
  useReadNotification,
} from "@/lib/api/queries";
import { useAppContext } from "@/context/app";
import { FullScreenMessage } from "@/components/FullScreenMessage";
import { queryClient } from "@/lib/providers/query-client";

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
  }),
});

function handleRegistrationError(errorMessage: string) {
  alert(errorMessage);
  throw new Error(errorMessage);
}

export default function AppLayout() {
  const [setupCompleted, setSetupCompleted] = useState(false);
  const [expoPushToken, setExpoPushToken] = useState("");
  const [notification, setNotification] = useState<
    Notifications.Notification | undefined
  >(undefined);
  const notificationListener = useRef<Notifications.EventSubscription>();
  const responseListener = useRef<Notifications.EventSubscription>();
  const { groupId, cohortId, setGroupId, setCohortId, chatClient, userId } =
    useAppContext();
  const { mutate: registerPushNotificationToken } =
    useRegisterPushNotificationToken();
  const { mutate: markNotificationAsRead } = useReadNotification();

  useEffect(() => {
    SuperTokens.doesSessionExist().then((exists) => {
      if (!exists) {
        router.replace("/sign-in");
      }
    });
  }, []);

  async function registerForPushNotificationsAsync() {
    if (Platform.OS === "android") {
      Notifications.setNotificationChannelAsync("default", {
        name: "default",
        importance: Notifications.AndroidImportance.MAX,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: "#FF231F7C",
      });
    }

    if (Device.isDevice) {
      const { status: existingStatus } =
        await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      if (existingStatus !== "granted") {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      if (finalStatus !== "granted") {
        handleRegistrationError(
          "Permission not granted to get push token for push notification!",
        );
        return;
      }
      const projectId =
        Constants?.expoConfig?.extra?.eas?.projectId ??
        Constants?.easConfig?.projectId;

      if (!projectId) {
        handleRegistrationError("Project ID not found");
      }

      try {
        const pushTokenString = (
          await Notifications.getExpoPushTokenAsync({ projectId })
        ).data;
        console.log("expo push token: ", pushTokenString);
        const fcmToken = await messaging(getApp()).getToken();
        console.log("fcm token: ", fcmToken);
        return fcmToken;
      } catch (e: unknown) {
        handleRegistrationError(`${e}`);
      }
    } else {
      handleRegistrationError(
        "Must use physical device for push notifications",
      );
    }
  }

  useEffect(() => {
    registerForPushNotificationsAsync()
      .then((token) => {
        setExpoPushToken(token ?? "");
        registerPushNotificationToken({
          platform: Platform.OS,
          token: token ?? "",
        });
      })
      .catch((error: any) => setExpoPushToken(`${error}`));

    notificationListener.current =
      Notifications.addNotificationReceivedListener((notification) => {
        setNotification(notification);

        // Invalidate notifications query cache when a new notification is received
        console.log(
          "New notification received, invalidating notifications cache",
        );
        queryClient.invalidateQueries({ queryKey: ["notifications"] });
      });

    responseListener.current =
      Notifications.addNotificationResponseReceivedListener((response) => {
        console.log("response received", response);
        const data = response.notification.request?.content?.data;

        if (data?.id) {
          console.log("Marking notification as read:", data.id);
          markNotificationAsRead({ notificationId: data.id.toString() });
        }

        // Handle navigation based on notification data
        if (data?.link && typeof data.link === "string") {
          // Extract the path from the full URL if router.push expects a path
          try {
            const url = new URL(data.link);
            router.push((url.pathname + url.search + url.hash) as any);
          } catch (e) {
            console.error("Error parsing notification link URL:", e);
            // Fallback to pushing the whole link if it's not a full URL or parsing fails
            router.push(data.link as any);
          }
        } else if (data?.sender === "stream.chat") {
          router.push("/(app)/(tabs)/chats");
          router.push(`/(app)/chat/channel/${data.channel_id}`);
        }
      });

    return () => {
      notificationListener.current &&
        Notifications.removeNotificationSubscription(
          notificationListener.current,
        );
      responseListener.current &&
        Notifications.removeNotificationSubscription(responseListener.current);
    };
  }, []);

  const {
    data: profileData,
    isLoading: isLoadingProfile,
    error: profileError,
  } = useProfile();
  const {
    data: myGroupsData,
    isLoading: isLoadingGroups,
    error: groupsError,
  } = useMyGroups();

  useEffect(() => {
    if (profileError || groupsError) {
      router.replace("/sign-in");
    }
  }, [profileError, groupsError]);

  useEffect(() => {
    if (
      profileData?.data &&
      (!profileData.data.lastName || !profileData.data.firstName)
    ) {
      router.replace("/setup-profile");
    }
  }, [profileData]);

  useEffect(() => {
    if (
      (!groupId || !cohortId) &&
      myGroupsData?.groups &&
      myGroupsData.groups.order.length > 0
    ) {
      const firstGroup = myGroupsData.groups.byId[myGroupsData.groups.order[0]];
      const firstCohort = firstGroup.joinedCohorts[0];
      if (firstGroup && firstCohort) {
        setGroupId(String(firstGroup.externalId));
        setCohortId(String(firstCohort.externalId));
      }
      setSetupCompleted(true);
    } else {
      setSetupCompleted(true);
    }
  }, [myGroupsData, groupId, cohortId, setGroupId, setCohortId]);

  useEffect(() => {
    if (!chatClient) return;
    (async () => {
      const authStatus = await messaging(getApp()).requestPermission();
      const enabled =
        authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
        authStatus === messaging.AuthorizationStatus.PROVISIONAL;
      if (!enabled) {
        console.warn("FCM permission not granted");
        return;
      }
      console.log("here here here here");
      try {
        const fcmToken = await messaging(getApp()).getToken();
        console.log("Registering FCM for Stream:", fcmToken);
        console.log("here here here here here");
        await chatClient.addDevice(
          fcmToken,
          "firebase",
          undefined,
          "push-notifications",
        );
        console.log("Successfully registered device with Stream:", fcmToken);
        const devices = await chatClient.getDevices();
        // console.log("Registered devices:", devices);
        registerPushNotificationToken({
          platform: Platform.OS,
          token: fcmToken,
        });
      } catch (error) {
        console.error("Error registering device with Stream:", error);
        if (error instanceof Error) {
          console.error(
            "Detailed FCM Token Error:",
            error.name,
            error.message,
            error.stack,
          );
        } else {
          console.error("Detailed FCM Token Error (non-Error object):", error);
        }
      }
    })();

    // Handle notification tap when app is in background
    const unsubscribeOnNotificationOpened = messaging(
      getApp(),
    ).onNotificationOpenedApp((remoteMessage) => {
      console.log(
        "Notification caused app to open from background state:",
        remoteMessage.notification,
        remoteMessage.data,
      );
      if (
        remoteMessage.data?.link &&
        typeof remoteMessage.data.link === "string"
      ) {
        try {
          const url = new URL(remoteMessage.data.link);
          router.push((url.pathname + url.search + url.hash) as any);
        } catch (e) {
          console.error(
            "Error parsing notification link URL from background state:",
            e,
          );
          router.push(remoteMessage.data.link as any);
        }
      } else if (
        remoteMessage.data?.sender === "stream.chat" &&
        remoteMessage.data?.channel_id
      ) {
        // Example for Stream chat, adjust as needed
        router.push("/(app)/(tabs)/chats");
        router.push(`/(app)/chat/channel/${remoteMessage.data.channel_id}`);
      } else if (remoteMessage.data?.cid) {
        // Keeping existing example for other potential Stream data
        router.push(`/chat/${remoteMessage.data.cid}` as any);
      }
    });

    // Handle notification tap when app is quit
    messaging(getApp())
      .getInitialNotification()
      .then((remoteMessage) => {
        if (remoteMessage) {
          console.log(
            "Notification caused app to open from quit state:",
            remoteMessage.notification,
            remoteMessage.data,
          );
          if (
            remoteMessage.data?.link &&
            typeof remoteMessage.data.link === "string"
          ) {
            try {
              const url = new URL(remoteMessage.data.link);
              router.push((url.pathname + url.search + url.hash) as any);
            } catch (e) {
              console.error(
                "Error parsing notification link URL from quit state:",
                e,
              );
              router.push(remoteMessage.data.link as any);
            }
          } else if (
            remoteMessage.data?.sender === "stream.chat" &&
            remoteMessage.data?.channel_id
          ) {
            router.push("/(app)/(tabs)/chats");
            router.push(`/(app)/chat/channel/${remoteMessage.data.channel_id}`);
          }
        }
      });

    // Handle foreground messages
    const unsubscribeOnMessage = messaging(getApp()).onMessage(
      async (remoteMessage) => {
        console.log("Foreground FCM Message data:", remoteMessage.data);
        // Display the notification using expo-notifications
        Notifications.scheduleNotificationAsync({
          content: {
            title: remoteMessage.notification?.title || "New Message",
            body: remoteMessage.notification?.body,
            data: remoteMessage.data,
          },
          trigger: null,
        });
      },
    );

    const unsubscribeOnTokenRefresh = messaging(getApp()).onTokenRefresh(
      async (newToken) => {
        console.log("FCM token refreshed:", newToken);
        await chatClient.addDevice(newToken, "firebase");
        registerPushNotificationToken({
          platform: Platform.OS,
          token: newToken,
        });
      },
    );
    return () => {
      unsubscribeOnTokenRefresh();
      unsubscribeOnNotificationOpened();
      unsubscribeOnMessage(); // Cleanup foreground listener
    };
  }, [chatClient, registerPushNotificationToken]);

  if (isLoadingGroups || isLoadingProfile) {
    return <FullScreenMessage text="Loading..." />;
  }

  if (profileError || groupsError) {
    return <FullScreenMessage text="Failed to load data" />;
  }

  if (!setupCompleted) {
    return <FullScreenMessage text="Setting up..." />;
  }

  // IMPORTANT: need to wait the chatClient too
  if (!chatClient) {
    return <FullScreenMessage text="Setting up chat client..." />;
  }

  return (
    <Chat client={chatClient}>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen
          name="group-selection"
          options={{
            presentation: "modal",
          }}
        />
        <Stack.Screen name="groups/[groupId]/index" />
        <Stack.Screen
          name="groups/[groupId]/cohort-selection"
          options={{ presentation: "modal" }}
        />
        <Stack.Screen
          name="groups/[groupId]/cohorts/[cohortId]/modules/[moduleId]/live-classes/[id]/index"
          options={{
            presentation: "modal",
          }}
        />
        <Stack.Screen
          name="profile/[id]"
          options={{
            presentation: "modal",
          }}
        />
      </Stack>
    </Chat>
  );
}
