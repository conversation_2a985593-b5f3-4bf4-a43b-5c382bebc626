import {
  View,
  Text,
  StyleSheet,
  Pressable,
  Platform,
  Share,
  ActivityIndicator,
} from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { MaterialIcons, Feather } from "@expo/vector-icons";
import Animated, {
  interpolate,
  useAnimatedRef,
  useAnimatedStyle,
  useScrollViewOffset,
  useAnimatedScrollHandler,
} from "react-native-reanimated";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { Course } from "@/components/modules/group/Course";
import { Bio } from "@/components/modules/group/Bio";
import { useGroup, useGroupCohorts } from "@/lib/api/queries";
import { Module } from "@/lib/api/types";

const HEADER_HEIGHT = 450;

const GroupFeed = () => {
  const scrollRef = useAnimatedRef<Animated.ScrollView>();
  const scrollOffset = useScrollViewOffset(
    scrollRef.current ? scrollRef : null,
  );
  const scrollHandler = useAnimatedScrollHandler((event) => {
    scrollOffset.value = event.contentOffset.y;
  });
  const imageAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: interpolate(
            scrollOffset.value,
            [-HEADER_HEIGHT, 0, HEADER_HEIGHT],
            [-HEADER_HEIGHT / 2, 0, HEADER_HEIGHT * 0.75],
          ),
        },
      ],
    };
  });

  const { groupId: id } = useLocalSearchParams<{ groupId: string }>();

  const insets = useSafeAreaInsets();

  const { isPending, error, data } = useGroup(id);
  const { data: cohortsData } = useGroupCohorts(id);

  const hasOpenCohorts =
    (cohortsData?.data?.cohorts?.filter((cohort) => !cohort.isDefault)
      ?.length ?? 0) > 0;

  const handleBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.replace("/");
    }
  };

  if (isPending) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>Error: {error.message}</Text>
      </View>
    );
  }

  const group = data?.data;

  const handleShare = async () => {
    const url = `${process.env.EXPO_PUBLIC_APP_BASE_URL}/groups/${id}`;
    try {
      if (Platform.OS === "android") {
        await Share.share({
          message: url,
        });
      } else {
        await Share.share({
          message: "Check out this group!",
          url,
        });
      }
    } catch (error) {
      console.error("Error sharing link:", error);
    }
  };

  return (
    <View style={[styles.container]}>
      <Animated.ScrollView
        ref={scrollRef}
        style={styles.content}
        onScroll={scrollHandler}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Image */}
        <Animated.View style={styles.headerContainer}>
          <Animated.Image
            source={{ uri: group?.bannerImage }}
            style={[styles.headerImage, imageAnimatedStyle]}
          />
          <LinearGradient
            colors={["transparent", "rgba(0,0,0,0.7)", "rgba(0,0,0, 1)"]}
            style={styles.headerGradient}
          >
            <View style={styles.headerContent}>
              <Text style={styles.title}>{group?.name}</Text>
              <Text
                style={styles.description}
                numberOfLines={3}
                ellipsizeMode="tail"
              >
                {group?.description}
              </Text>
            </View>
            {/* Group Info */}
            <View style={styles.groupInfo}>
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <Feather name="users" size={16} color="#838A94" />
                  <Text style={styles.statText}>
                    {group?.totalMembers} Members
                  </Text>
                </View>
                <View style={styles.statItem}>
                  <Feather name="list" size={16} color="#838A94" />
                  <Text style={styles.statText}>
                    {group?.totalLessons} Lessons
                  </Text>
                </View>
              </View>
            </View>
          </LinearGradient>
        </Animated.View>

        <View style={{ backgroundColor: "#000" }}>
          {/* Group Bio */}
          {group?.bio && (
            <View style={styles.aboutBioContainer}>
              <Bio bio={group.bio} />
            </View>
          )}

          {/* About Creator */}
          <Pressable
            onPress={() => {
              if (data?.data?.creatorSupertokensUserId) {
                router.push({
                  pathname: "/profile/[id]",
                  params: { id: data.data.creatorSupertokensUserId },
                });
              }
            }}
          >
            <View style={styles.aboutCreatorContainer}>
              <Text style={styles.sectionTitle}>About Creator</Text>
              <View style={styles.creatorInfo}>
                <View style={styles.creatorProfile}>
                  <MaterialIcons name="person" size={20} color="#fff" />
                  <Text style={styles.creatorName}>
                    {data?.data?.creatorName}
                  </Text>
                </View>
                <Text style={styles.creatorBio}>{data?.data?.creatorBio}</Text>
              </View>
            </View>
          </Pressable>

          {/* Course TOC */}
          {data?.data?.defaultCohortModules?.map(
            (module: Module, index: number) => {
              if (module.type === "course" && module.config.courseId) {
                return (
                  <View key={index} style={{ padding: 16 }}>
                    <Course
                      courseId={module.config.courseId}
                      showProgress={false}
                    />
                  </View>
                );
              }
              return null;
            },
          )}
        </View>
      </Animated.ScrollView>

      <View style={styles.headerControls}>
        <Pressable onPress={handleBack}>
          <MaterialIcons name="arrow-back" size={24} color="white" />
        </Pressable>
        <View style={styles.headerRightControls}>
          <Pressable onPress={handleShare}>
            <Feather name="share-2" size={24} color="white" />
          </Pressable>
          {/* <Pressable>
            <Feather name="more-horizontal" size={24} color="white" />
          </Pressable> */}
        </View>
      </View>

      {/* Bottom Bar */}
      <View style={[styles.bottomBar, { paddingBottom: insets.bottom + 16 }]}>
        <View style={styles.bottomBarContent}>
          {hasOpenCohorts ? (
            <Text style={styles.startingDate}>Open for enrollment</Text>
          ) : (
            <Text style={styles.noCohorts}>No open cohorts</Text>
          )}
          {hasOpenCohorts && (
            <Pressable
              style={styles.joinButton}
              onPress={() => {
                router.push(`/groups/${id}/cohort-selection`);
              }}
            >
              <Text style={styles.joinButtonText}>Join Now</Text>
            </Pressable>
          )}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  bottomBar: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "#1A1A1A",
    borderTopWidth: 1,
    borderTopColor: "#1A1A1A",
  },
  bottomBarContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingTop: 16,
    gap: 16,
  },
  startingDate: {
    color: "#fff",
    fontSize: 14,
  },
  noCohorts: {
    color: "#4a4a4a",
    fontSize: 14,
    flex: 1,
    fontWeight: "700",
    textAlign: "center",
  },
  joinButton: {
    backgroundColor: "#6366F1",
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  joinButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    color: "#fff",
    fontSize: 16,
  },
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  headerContainer: {
    height: HEADER_HEIGHT,
    position: "relative",
  },
  headerImage: {
    width: "100%",
    height: "100%",
    position: "absolute",
  },
  headerGradient: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: "100%",
    justifyContent: "flex-end",
    padding: 16,
  },
  headerContent: {
    marginBottom: 16,
  },
  headerControls: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-between",
    padding: 16,
    paddingTop: 48, // Added extra padding for status bar
  },
  headerRightControls: {
    flexDirection: "row",
    gap: 16,
  },
  content: {
    flex: 1,
    backgroundColor: "#000",
  },
  contentContainer: {
    paddingBottom: 100, // Add padding for bottom bar
  },
  aboutCreatorContainer: {
    marginTop: 24,
    paddingHorizontal: 16,
  },
  sectionTitle: {
    color: "#fff",
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 16,
  },
  creatorInfo: {
    backgroundColor: "#1A1A1A",
    borderRadius: 12,
    padding: 16,
  },
  creatorProfile: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 12,
  },
  creatorName: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "500",
  },
  creatorBio: {
    color: "#838A94",
    fontSize: 14,
    lineHeight: 20,
  },
  groupInfo: {
    // marginBottom: 5,
  },
  title: {
    color: "#fff",
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  description: {
    color: "#D9D9D9",
    fontSize: 16,
    lineHeight: 24,
  },
  readMore: {
    color: "#4A90E2",
    fontWeight: "bold",
  },
  statsContainer: {
    flexDirection: "row",
    gap: 32,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  statText: {
    color: "#838A94",
    fontSize: 12,
  },
  aboutBioContainer: {
    marginTop: 16,
  },
});

export default GroupFeed;
