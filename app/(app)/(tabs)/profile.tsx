import React, { useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  Image,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from "react-native";
import { router } from "expo-router";
import SuperTokens from "supertokens-react-native";
import * as ImagePicker from "expo-image-picker";
import {
  CircleUserRound,
  ShieldHalf,
  Bell,
  Lock,
  HelpCircle,
  Pencil,
  LogOut,
  ChevronRight,
  ReceiptText,
  Camera,
} from "lucide-react-native";
import { useQueryClient } from "@tanstack/react-query";
import Constants from "expo-constants";

import { useAppContext } from "@/context/app";
import {
  useProfile,
  useEditProfile,
  useGetUploadUrl,
  useUploadAvatar,
} from "@/lib/api/queries";

interface MenuItem {
  icon: React.ElementType;
  title: string;
  onPress: () => void;
  section?: string;
}

const ProfileScreen: React.FC = () => {
  const queryClient = useQueryClient();
  const { cleanup } = useAppContext();
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);

  const { data: profileData } = useProfile();
  const { mutate: editProfile } = useEditProfile();
  const { mutate: getUploadUrl } = useGetUploadUrl();
  const { mutate: uploadAvatar } = useUploadAvatar();

  const handleSignOut = async () => {
    await SuperTokens.signOut();

    // IMPORTANT: cleanup all the context state here
    cleanup();
    queryClient.clear();

    router.replace("/sign-in");
  };

  const handleAvatarPress = () => {
    Alert.alert(
      "Update Avatar",
      "Choose how you'd like to update your avatar",
      [
        { text: "Camera", onPress: () => openImagePicker("camera") },
        { text: "Photo Library", onPress: () => openImagePicker("library") },
        { text: "Cancel", style: "cancel" },
      ],
    );
  };

  const openImagePicker = async (source: "camera" | "library") => {
    try {
      let result: ImagePicker.ImagePickerResult;

      if (source === "camera") {
        // Request camera permissions
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Camera permission is required to take photos",
          );
          return;
        }

        // Launch camera
        result = await ImagePicker.launchCameraAsync({
          mediaTypes: "images",
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });
      } else {
        // Request media library permissions
        const { status } =
          await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== "granted") {
          Alert.alert(
            "Permission needed",
            "Photo library permission is required to select photos",
          );
          return;
        }

        // Launch image library
        result = await ImagePicker.launchImageLibraryAsync({
          mediaTypes: "images",
          allowsEditing: true,
          aspect: [1, 1],
          quality: 0.8,
        });
      }

      if (!result.canceled && result.assets[0]) {
        await uploadNewAvatar(result.assets[0]);
      }
    } catch (error) {
      console.error("Error opening image picker:", error);
      Alert.alert("Error", "Failed to open image picker");
    }
  };

  const uploadNewAvatar = async (asset: ImagePicker.ImagePickerAsset) => {
    setIsUploadingAvatar(true);

    try {
      // Determine content type from the asset
      const getContentType = (uri: string): string => {
        const extension = uri.split(".").pop()?.toLowerCase();
        switch (extension) {
          case "jpg":
          case "jpeg":
            return "image/jpeg";
          case "png":
            return "image/png";
          case "gif":
            return "image/gif";
          case "webp":
            return "image/webp";
          default:
            return "image/jpeg"; // fallback
        }
      };

      const contentType = getContentType(asset.uri);
      const fileExtension = contentType === "image/png" ? "png" : "jpg";
      const fileName = `avatar_${Date.now()}.${fileExtension}`;

      // Step 1: Get presigned URL
      getUploadUrl(
        { contentType, fileName },
        {
          onSuccess: ({ cdnUrl, uploadUrl }) => {
            // Step 2: Upload to presigned URL
            uploadAvatar(
              {
                uploadUrl,
                uri: asset.uri,
                fileType: contentType,
              },
              {
                onSuccess: () => {
                  // Step 3: Update profile with CDN URL
                  editProfile(
                    {
                      firstName: profileData?.data?.firstName || "",
                      lastName: profileData?.data?.lastName || "",
                      gender: profileData?.data?.gender || null,
                      avatarURL: cdnUrl,
                    },
                    {
                      onSuccess: () => {
                        Alert.alert("Success", "Avatar updated successfully!");
                        setIsUploadingAvatar(false);
                      },
                      onError: (error) => {
                        console.error("Error updating profile:", error);
                        Alert.alert("Error", "Failed to update profile");
                        setIsUploadingAvatar(false);
                      },
                    },
                  );
                },
                onError: (error) => {
                  console.error("Error uploading avatar:", error);
                  Alert.alert("Error", "Failed to upload avatar");
                  setIsUploadingAvatar(false);
                },
              },
            );
          },
          onError: (error) => {
            console.error("Error getting upload URL:", error);
            Alert.alert("Error", "Failed to get upload URL");
            setIsUploadingAvatar(false);
          },
        },
      );
    } catch (error) {
      console.error("Error uploading avatar:", error);
      Alert.alert("Error", "Failed to upload avatar");
      setIsUploadingAvatar(false);
    }
  };

  const menuItems: MenuItem[] = [
    {
      section: "Settings",
      icon: CircleUserRound,
      title: "Personal information",
      onPress: () => router.push("/profile/edit"),
    },
    {
      icon: ShieldHalf,
      title: "Login & Security",
      onPress: () => {},
    },
    {
      icon: Bell,
      title: "Notification",
      onPress: () => {},
    },
    {
      icon: Lock,
      title: "Privacy and Sharing",
      onPress: () => {},
    },
    {
      section: "Support",
      icon: HelpCircle,
      title: "Help Center",
      onPress: () => {},
    },
    {
      icon: Pencil,
      title: "Feedback",
      onPress: () => {},
    },
    {
      section: "Legal",
      icon: ReceiptText,
      title: "Terms Of Service",
      onPress: () => {},
    },
    {
      icon: ShieldHalf,
      title: "Privacy Policy",
      onPress: () => {},
    },
  ];

  const renderMenuItem = React.useCallback(
    ({ icon: Icon, title, onPress, section }: MenuItem) => (
      <React.Fragment key={title}>
        {section && <Text style={styles.sectionTitle}>{section}</Text>}
        <TouchableOpacity style={styles.menuItem} onPress={onPress}>
          <View style={styles.menuItemLeft}>
            <Icon size={20} color="#fff" />
            <Text style={styles.menuText}>{title}</Text>
          </View>
          <ChevronRight size={24} color="#fff" />
        </TouchableOpacity>
      </React.Fragment>
    ),
    [],
  );

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <Text style={styles.screenTitle}>Profile</Text>

        <View style={styles.header}>
          <View style={styles.profileInfo}>
            <TouchableOpacity
              style={styles.avatarContainer}
              onPress={handleAvatarPress}
              disabled={isUploadingAvatar}
            >
              <Image
                source={{
                  uri:
                    profileData?.data?.avatarUrl ||
                    "https://i.pravatar.cc/150?img=3",
                }}
                style={styles.avatar}
              />
              {isUploadingAvatar ? (
                <View style={styles.avatarOverlay}>
                  <ActivityIndicator size="small" color="#fff" />
                </View>
              ) : (
                <View style={styles.avatarOverlay}>
                  <Camera size={16} color="#fff" />
                </View>
              )}
            </TouchableOpacity>
            <View style={styles.nameContainer}>
              <Text style={styles.name}>
                {profileData?.data
                  ? `${profileData.data.firstName || ""} ${
                      profileData.data.lastName || ""
                    }`.trim()
                  : "Guest User"}
              </Text>
              <Text style={styles.location}>
                {profileData?.data?.location || "No location set"}
              </Text>
            </View>
          </View>

          <View style={styles.stats}>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>42</Text>
              <Text style={styles.statLabel}>Follower</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={styles.statNumber}>8</Text>
              <Text style={styles.statLabel}>Followings</Text>
            </View>
            <View style={styles.statItem}>
              <View style={styles.statNumberContainer}>
                <Text style={styles.statNumber}>12</Text>
                <Text style={styles.statUnit}>hours</Text>
              </View>
              <Text style={styles.statLabel}>Total Learning</Text>
            </View>
          </View>
        </View>

        <View style={styles.menuContainer}>
          {menuItems.map(renderMenuItem)}
        </View>

        <TouchableOpacity style={styles.logoutButton} onPress={handleSignOut}>
          <LogOut size={20} color="#999999" />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
        <Text style={styles.versionText}>
          Version {Constants.expoConfig?.version ?? "x.y.z"}
        </Text>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 16,
    paddingBottom: 48,
  },
  screenTitle: {
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
    marginBottom: 36,
  },
  header: {
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: "#262626",
    paddingBottom: 16,
  },
  profileInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 24,
  },
  avatarContainer: {
    position: "relative",
    marginRight: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  avatarOverlay: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#000",
  },
  nameContainer: {
    flex: 1,
  },
  name: {
    fontSize: 18,
    fontWeight: "700",
    color: "#fff",
    marginBottom: 3,
  },
  location: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.5)",
  },
  stats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  statItem: {
    alignItems: "flex-start",
  },
  statNumber: {
    fontSize: 24,
    fontWeight: "900",
    color: "#fff",
    lineHeight: 24,
    marginBottom: 2,
  },
  statLabel: {
    fontSize: 14,
    color: "rgba(255, 255, 255, 0.6)",
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: "500",
    color: "#9A9A9A",
    lineHeight: 20,
    marginTop: 16,
    marginBottom: 8,
  },
  menuContainer: {
    flex: 1,
  },
  menuItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#191919",
  },
  menuItemLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  menuText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#fff",
    lineHeight: 14,
  },
  bottomNav: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 16,
    paddingHorizontal: 24,
    backgroundColor: "#131313",
  },
  navItem: {
    width: 50,
    alignItems: "center",
    gap: 5,
  },
  navLabel: {
    fontSize: 10,
    color: "#fff",
  },
  statNumberContainer: {
    flexDirection: "row",
    alignItems: "baseline",
  },
  statUnit: {
    fontSize: 14,
    fontWeight: "800",
    color: "#fff",
    marginLeft: 4,
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#131313",
    paddingVertical: 16,
    borderRadius: 12,
    marginTop: 24,
    gap: 8,
  },
  logoutText: {
    color: "#999999",
    fontSize: 16,
    fontWeight: "600",
  },
  versionText: {
    color: "#666666",
    fontSize: 12,
    textAlign: "center",
    marginTop: 12,
  },
});

export default ProfileScreen;
