import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Pressable,
  ActivityIndicator,
} from "react-native";
import { router, useLocalSearchParams } from "expo-router";
import { MaterialIcons } from "@expo/vector-icons";
import { CheckCircle } from "lucide-react-native";
import { useCourse } from "@/lib/api/queries";

// Import the actual types from the API
import { Course, Lesson, Section } from "@/lib/api/types";

export default function TOCModal() {
  const { courseId, sectionId, lessonId } = useLocalSearchParams<{
    courseId: string;
    sectionId: string;
    lessonId: string;
  }>();
  const { data: courseData, isLoading, error } = useCourse(courseId);

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color="#fff" />
      </View>
    );
  }

  if (error || !courseData?.data) {
    return (
      <View style={[styles.container, styles.centered]}>
        <Text style={styles.errorText}>Error loading course content</Text>
      </View>
    );
  }

  const course = courseData.data;
  const currentLessonId = Number(lessonId);

  // Sort sections by their order
  const sortedSections = [...course.sections].sort(
    (a, b) => a.sectionOrder - b.sectionOrder,
  );

  const handleLessonPress = (lessonId: number, sectionId: number) => {
    router.back();
    router.push(
      `/courses/${courseId}/sections/${sectionId}/lessons/${lessonId}`,
    );
  };

  // Helper function to format video duration in minutes and seconds
  const formatDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, "0")}`;
  };

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        <View style={styles.header}>
          <MaterialIcons
            name="close"
            size={24}
            color="white"
            onPress={() => router.back()}
          />
          <Text style={styles.title}>{course.name}</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView
          style={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {sortedSections.map((section) => (
            <View key={section.id} style={styles.section}>
              <Text style={styles.sectionTitle}>{section.name}</Text>
              <View style={styles.lessonList}>
                {[...section.lessons]
                  .sort((a, b) => a.lessonOrder - b.lessonOrder)
                  .map((lesson) => {
                    const isCurrentLesson = lesson.id === currentLessonId;
                    const lessonType =
                      lesson.lessonContent?.contentType || "document";

                    return (
                      <Pressable
                        key={lesson.id}
                        style={[
                          styles.lessonItem,
                          isCurrentLesson && styles.activeLesson,
                        ]}
                        onPress={() => handleLessonPress(lesson.id, section.id)}
                      >
                        <View style={styles.lessonLeft}>
                          <MaterialIcons
                            name={
                              lessonType === "video"
                                ? "play-circle-outline"
                                : "description"
                            }
                            size={24}
                            color={isCurrentLesson ? "#FFF" : "#9BA1A6"}
                          />
                          <Text
                            style={[
                              styles.lessonTitle,
                              isCurrentLesson && styles.activeLessonText,
                            ]}
                          >
                            {lesson.title}
                          </Text>
                        </View>
                        <View style={styles.lessonRight}>
                          {lesson.lessonContent?.contentType === "video" &&
                          lesson.lessonContent.metadata?.videoLength ? (
                            <Text
                              style={[
                                styles.duration,
                                isCurrentLesson && styles.activeLessonText,
                              ]}
                            >
                              {formatDuration(
                                lesson.lessonContent.metadata.videoLength,
                              )}
                            </Text>
                          ) : null}
                          {lesson.isCompleted ? (
                            <CheckCircle
                              size={20}
                              color="#10B981"
                              style={styles.checkIcon}
                            />
                          ) : (
                            <View style={styles.incompleteCircle} />
                          )}
                        </View>
                      </Pressable>
                    );
                  })}
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1A1A1A",
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  errorText: {
    color: "#ff4444",
    fontSize: 16,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
  },
  title: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "700",
  },
  scrollContent: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginBottom: 16,
    padding: 16,
    backgroundColor: "#111",
    borderWidth: 1,
    borderColor: "#1E1E1E",
    borderRadius: 8,
  },
  sectionTitle: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "700",
    marginBottom: 16,
  },
  lessonList: {
    gap: 2,
  },
  lessonItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 8,
    borderRadius: 4,
  },
  lessonLeft: {
    flexDirection: "row",
    alignItems: "center",
    gap: 14,
    flex: 1,
  },
  lessonTitle: {
    color: "#FFF",
    fontSize: 12,
    fontWeight: "500",
    flex: 1,
  },
  lessonRight: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  duration: {
    color: "#9BA1A6",
    fontSize: 12,
    fontWeight: "500",
  },
  checkIcon: {
    marginLeft: 4,
  },
  incompleteCircle: {
    width: 16,
    height: 16,
  },
  activeLesson: {
    backgroundColor: "rgba(255, 255, 255, 0.1)",
  },
  activeLessonText: {
    color: "#FFF",
  },
});
