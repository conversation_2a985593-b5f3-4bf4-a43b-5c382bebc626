import { useState, useEffect } from "react";
import {
  StyleSheet,
  TextInput,
  View,
  Text,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  ImageBackground,
  Image,
} from "react-native";
import { router } from "expo-router";
import SuperTokens from "supertokens-react-native";

import { navigateTo } from "@/utils/navigation";
import { useAppContext } from "@/context/app";
import { GoogleButton } from "@/components/GoogleButton";

export default function SignIn() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailError, setEmailError] = useState("");
  const [authError, setAuthError] = useState("");

  const { setIsAuthenticated } = useAppContext();

  useEffect(() => {
    SuperTokens.doesSessionExist().then((exists) => {
      if (exists) {
        console.log("THE USER IS LOGGED IN");
        router.replace("/(app)/(tabs)");
      }
    });
  }, []);

  const handleSignIn = async () => {
    if (isSubmitting) return;

    // Reset errors
    setEmailError("");
    setAuthError("");

    setIsSubmitting(true);
    try {
      const response = await fetch(
        process.env.EXPO_PUBLIC_API_DOMAIN + "/auth/signin",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          body: JSON.stringify({
            formFields: [
              { id: "email", value: email },
              { id: "password", value: password },
            ],
          }),
        },
      );

      const data = await response.json();

      switch (data.status) {
        case "OK":
          console.log("signin successfully");
          setIsAuthenticated(true);
          router.replace("/(app)/(tabs)");
          break;
        case "WRONG_CREDENTIALS_ERROR":
          setAuthError("Invalid email or password");
          break;
        case "FIELD_ERROR":
          setEmailError("Please enter a valid email address");
          break;
        case "SIGN_IN_NOT_ALLOWED":
          setAuthError(`Sign in not allowed. Support code: ${data.reason}`);
          break;
        case "GENERAL_ERROR":
          setAuthError(data.message || "An unexpected error occurred");
          break;
        default:
          setAuthError("An unexpected error occurred");
      }
    } catch (e) {
      console.error("Sign in failed:", e instanceof Error ? e.message : e);
      setAuthError("Failed to connect to the server");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <ImageBackground
      source={require("../assets/images/tree-bg.png")}
      style={styles.backgroundImage}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <View style={styles.innerContainer}>
          <View style={styles.contentWrapper}>
            <Image
              source={require("../assets/images/icon.png")}
              style={styles.logo}
              resizeMode="contain"
            />

            <View style={styles.formContainer}>
              <Text style={styles.loginTitle}>Sign in</Text>
              <Text style={styles.loginSubtitle}>
                Sign in to your Sphere account
              </Text>

              <TextInput
                style={[styles.input, emailError ? styles.inputError : null]}
                placeholder="Email"
                placeholderTextColor="#999"
                value={email}
                onChangeText={setEmail}
                autoCapitalize="none"
                keyboardType="email-address"
                editable={!isSubmitting}
              />
              {emailError ? (
                <Text style={styles.errorText}>{emailError}</Text>
              ) : null}

              <TextInput
                style={styles.input}
                placeholder="Password"
                placeholderTextColor="#999"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
                editable={!isSubmitting}
              />

              {authError ? (
                <Text style={styles.errorText}>{authError}</Text>
              ) : null}

              <TouchableOpacity
                style={[styles.button, isSubmitting && styles.buttonDisabled]}
                onPress={handleSignIn}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>Sign In</Text>
                )}
              </TouchableOpacity>

              <TouchableOpacity onPress={() => {}}>
                <Text style={styles.forgotPassword}>Forgot Password ?</Text>
              </TouchableOpacity>

              <View style={styles.divider}>
                <View style={styles.dividerLine} />
                <Text style={styles.dividerText}>OR</Text>
                <View style={styles.dividerLine} />
              </View>

              <GoogleButton />

              <View style={styles.signupContainer}>
                <Text style={styles.signupText}>New User? </Text>
                <TouchableOpacity onPress={navigateTo.signUp}>
                  <Text style={styles.signupLink}>Sign Up Now</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </KeyboardAvoidingView>
    </ImageBackground>
  );
}

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: "100%",
    height: "100%",
  },
  container: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
  },
  innerContainer: {
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: 20,
  },
  contentWrapper: {
    alignItems: "center",
  },
  logo: {
    width: 60,
    height: 60,
    marginBottom: 40,
  },
  formContainer: {
    width: "100%",
    maxWidth: 400,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#fff",
    marginBottom: 8,
    textAlign: "center",
  },
  loginSubtitle: {
    fontSize: 14,
    color: "#ccc",
    marginBottom: 24,
    textAlign: "center",
  },
  input: {
    width: "100%",
    height: 48,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 8,
    marginBottom: 12,
    paddingHorizontal: 15,
    fontSize: 16,
    color: "#fff",
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.2)",
  },
  inputError: {
    borderColor: "#ff4545",
  },
  button: {
    backgroundColor: "#4B5EAB",
    paddingVertical: 14,
    paddingHorizontal: 15,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 20,
  },
  buttonDisabled: {
    backgroundColor: "rgba(75, 94, 171, 0.5)",
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  googleButton: {
    backgroundColor: "#FFFFFF",
    padding: 15,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 24,
  },
  googleButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: 8,
  },
  googleButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "600",
  },
  errorText: {
    color: "#ff4545",
    fontSize: 12,
    marginTop: -12,
    marginBottom: 12,
  },
  forgotPassword: {
    color: "#ccc",
    textAlign: "center",
    marginTop: 12,
    fontSize: 14,
  },
  signupContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 20,
  },
  signupText: {
    color: "#fff",
  },
  signupLink: {
    color: "#fff",
    fontWeight: "bold",
  },
  divider: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 20,
    marginBottom: 8,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  dividerText: {
    color: "#ccc",
    paddingHorizontal: 10,
    fontSize: 14,
  },
});
